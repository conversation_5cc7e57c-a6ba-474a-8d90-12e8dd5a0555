\documentclass[preview]{standalone}
\usepackage[english]{babel}
\usepackage{amsmath}
\usepackage{amssymb}
\begin{document}
\begin{align*}
\begin{array}{l}\texttt{int trap(vector<int>\& height) \{} \\\texttt{    int left = 0, right = height.size() - 1;} \\\texttt{    int left\_max = 0, right\_max = 0;} \\\texttt{    int result = 0;} \\\texttt{} \\\texttt{    while (left < right) \{} \\\texttt{        if (height[left] < height[right]) \{} \\\texttt{            if (height[left] >= left\_max)} \\\texttt{                left\_max = height[left];} \\\texttt{            else} \\\texttt{                result += left\_max - height[left];} \\\texttt{            left++;} \\\texttt{        \} else \{} \\\texttt{            // 类似处理右边} \\\texttt{        \}} \\\texttt{    \}} \\\texttt{    return result;} \\\texttt{\}}\end{array}
\end{align*}
\end{document}