基于manim(最新版)实现可视化视频

使用 context7 mcp 服务

你需要注意的是有些动画显示出来就可以移除了 或者 用 移动把它放到屏幕的其他位置，防止影响主要的动画

动画尽可能无缝流畅转场

代码区域使用tex进行实现代码，代码需要高亮，然后不同代码之间无缝流畅转场，不需要说明文字

这是视频内容：

力扣第42题接雨水


力扣第42题：**接雨水**

---

### 🌧️ 题目描述

给定 `n` 个非负整数表示每个宽度为 1 的柱子的高度图，计算按此排列的柱子，下雨之后能接多少雨水。

**示例：**

```
输入：height = [0,1,0,2,1,0,1,3,2,1,2,1]
输出：6
解释：上面是由数组 [0,1,0,2,1,0,1,3,2,1,2,1] 表示的高度图，在这种情况下，可以接 6 个单位的雨水（蓝色部分表示雨水）。
```

---

### ✅ 解题思路

要计算能接多少雨水，关键在于：**对于每个位置 i，能存多少水取决于它左右两边的“最高柱子”中的较小值**。

具体来说：

- 在位置 `i` 能接的雨水量为：
  ```
  water[i] = min(lmax[i], rmax[i]) - height[i]
  ```
  其中：
  - `lmax[i]` 是 `i` 左边（包括 i）的最大高度
  - `rmax[i]` 是 `i` 右边（包括 i）的最大高度
  - 如果 `min(lmax, rmax) <= height[i]`，则该位置无法存水

我们目标是求所有位置的 `water[i]` 的总和。

---

字幕：这是对应的C++代码

```cpp
auto size = height.size();
std::vector<int> left(size + 1), right(size + 1);

for (std::size_t i = 0; i != size; ++i) {
    left[i + 1] = std::max(left[i], height[i]);
}
for (std::size_t i = size - 1; i + 1 != 0; --i) {
    right[i] = std::max(right[i + 1], height[i]);
}

int ans = 0;
for (std::size_t i = 0; i < size; ++i) {
    ans += std::min(left[i + 1], right[i + 1]) - height[i];
}
```

字幕：当然你如果对 c++ 比较了解，我们可以用 std::partial_sum 来代替循环

```cpp
auto size = height.size();
std::vector<int> left(size), right(size);

std::partial_sum(height.begin(), height.end(), left.begin(), std::ranges::max);
std::partial_sum(height.rbegin(), height.rend(), right.rbegin(), std::ranges::max);

int ans = 0;
for (std::size_t i = 0; i < size; ++i) {
    ans += std::min(left[i], right[i]) - height[i];
}
```

。

如果我们换种思路，相当于把所有的雨水也看成墙，那么我们只需要求出来总面积，然后减去所有柱子的高度，那么我们可以写出这个代码（这里你需要把这个可视化实现出来）

```cpp
std::vector<int> left(height.size()), right(height.size());

std::partial_sum(height.begin(), height.end(), left.begin(), std::ranges::max);
std::partial_sum(height.rbegin(), height.rend(), right.rbegin(), std::ranges::max);

int ans = 0;
for (std::size_t i = 0; i < height.size(); ++i) {
    ans += std::min(left[i], right[i]);
}
ans -= std::accumulate(height.begin(), height.end(), 0);
```

认真观察可以发现，两个代码其实是一样的，只是一个在循环里面依次减去了height[i], 一个是减去总共的；但是两个思想确是从不同角度实现的