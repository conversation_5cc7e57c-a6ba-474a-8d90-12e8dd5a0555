from manim import *
import numpy as np

class RainWaterTrap(Scene):
    def construct(self):
        # 设置背景颜色
        self.camera.background_color = "#1a1a1a"
        
        # 示例数据
        height = [0, 1, 0, 2, 1, 0, 1, 3, 2, 1, 2, 1]
        
        # 显示标题
        self.show_title()
        
        # 显示问题和图表
        self.show_problem_and_chart(height)
        
        # 显示解题思路
        self.show_solution_approach(height)
        
        # 显示代码实现
        self.show_code_implementations()
        
        # 最终演示
        self.show_final_demo(height)

    def show_title(self):
        """显示标题"""
        title = Text("力扣第42题：接雨水", font_size=48, color=WHITE, weight=BOLD)
        title.move_to(ORIGIN)
        
        self.play(Write(title), run_time=2)
        self.wait(1)
        
        # 移动到顶部
        self.play(title.animate.scale(0.8).to_edge(UP), run_time=1)
        self.title = title

    def show_problem_and_chart(self, height):
        """显示问题描述和柱状图"""
        # 问题描述
        problem_text = VGroup(
            Text("给定 n 个非负整数表示每个宽度为 1 的柱子的高度图", font_size=22, color=YELLOW),
            Text("计算按此排列的柱子，下雨之后能接多少雨水", font_size=22, color=YELLOW)
        ).arrange(DOWN, buff=0.2)
        problem_text.move_to(UP * 2.2)
        
        self.play(Write(problem_text), run_time=2)
        self.wait(1)
        
        # 示例数据
        example = Text("示例：height = [0,1,0,2,1,0,1,3,2,1,2,1]", font_size=18, color=BLUE_C)
        example.next_to(problem_text, DOWN, buff=0.4)
        self.play(Write(example), run_time=1)
        self.wait(1)
        
        # 创建柱状图
        self.create_chart(height)
        
        # 清除文字
        self.play(FadeOut(problem_text), FadeOut(example), run_time=1)

    def create_chart(self, height):
        """创建柱状图"""
        # 创建坐标轴
        axes = Axes(
            x_range=[-0.5, len(height) - 0.5, 1],
            y_range=[0, max(height) + 1, 1],
            x_length=10,
            y_length=3.5,
            axis_config={"color": GRAY, "stroke_width": 2}
        )
        axes.move_to(DOWN * 0.3)
        
        self.play(Create(axes), run_time=1)
        
        # 创建柱子
        bars = VGroup()
        for i, h in enumerate(height):
            bar = Rectangle(
                width=0.6,
                height=h * 0.7 if h > 0 else 0.05,
                fill_color=BLUE_D if h > 0 else GRAY_D,
                fill_opacity=0.8,
                stroke_color=WHITE,
                stroke_width=2
            )
            bar.move_to(axes.c2p(i, (h * 0.7)/2 if h > 0 else 0.025))
            bars.add(bar)
        
        # 创建标签
        height_labels = VGroup()
        index_labels = VGroup()
        
        for i, h in enumerate(height):
            # 高度标签
            h_label = Text(str(h), font_size=16, color=YELLOW, weight=BOLD)
            h_label.move_to(axes.c2p(i, h * 0.7 + 0.2))
            height_labels.add(h_label)
            
            # 索引标签
            i_label = Text(str(i), font_size=14, color=WHITE)
            i_label.move_to(axes.c2p(i, -0.3))
            index_labels.add(i_label)
        
        # 动画显示
        for i, (bar, h_label, i_label) in enumerate(zip(bars, height_labels, index_labels)):
            self.play(
                GrowFromEdge(bar, DOWN),
                Write(h_label),
                Write(i_label),
                run_time=0.2
            )
        
        self.axes = axes
        self.bars = bars
        self.height_labels = height_labels
        self.index_labels = index_labels
        self.wait(1)

    def show_solution_approach(self, height):
        """显示解题思路"""
        # 清除图表
        self.play(
            FadeOut(self.axes),
            FadeOut(self.bars),
            FadeOut(self.height_labels),
            FadeOut(self.index_labels),
            run_time=1
        )
        
        # 解题思路标题
        title = Text("解题思路", font_size=40, color=GREEN, weight=BOLD)
        title.move_to(UP * 3)
        self.play(Write(title), run_time=1.5)
        
        # 思路步骤
        steps = VGroup(
            Text("对于每个位置 i，能存的水量 = min(左边最高, 右边最高) - height[i]", 
                 font_size=20, color=YELLOW),
            Text("使用双指针法：从两端向中间移动", font_size=20, color=YELLOW),
            Text("维护 left_max 和 right_max", font_size=20, color=YELLOW),
            Text("移动较小一边的指针", font_size=20, color=YELLOW)
        ).arrange(DOWN, buff=0.4)
        steps.move_to(UP * 1)
        
        for step in steps:
            self.play(Write(step), run_time=1)
            self.wait(0.5)
        
        self.wait(2)
        
        # 清除思路
        self.play(FadeOut(title), FadeOut(steps), run_time=1)

    def show_code_implementations(self):
        """显示代码实现"""
        # 代码标题
        code_title = Text("C++ 代码实现", font_size=36, color=BLUE, weight=BOLD)
        code_title.to_edge(UP)
        self.play(Write(code_title), run_time=1.5)

        # 方法一：双指针法
        method1_title = Text("方法一：双指针法", font_size=24, color=GREEN)
        method1_title.move_to(UP * 2.5)

        # 使用 Text 创建代码块，简单可靠
        code_lines_1 = [
            "int trap(vector<int>& height) {",
            "    int left = 0, right = height.size() - 1;",
            "    int left_max = 0, right_max = 0;",
            "    int result = 0;",
            "",
            "    while (left < right) {",
            "        if (height[left] < height[right]) {",
            "            if (height[left] >= left_max)",
            "                left_max = height[left];",
            "            else",
            "                result += left_max - height[left];",
            "            left++;",
            "        } else {",
            "            // 类似处理右边",
            "        }",
            "    }",
            "    return result;",
            "}"
        ]

        code1 = VGroup()
        for i, line in enumerate(code_lines_1):
            if line.strip():
                text_line = Text(line, font_size=16, color=WHITE, font="Courier New")
            else:
                text_line = Text(" ", font_size=16)
            text_line.move_to(UP * (1.8 - i * 0.2))
            code1.add(text_line)

        code1.scale(0.8)
        code1.move_to(DOWN * 0.2)

        self.play(Write(method1_title), run_time=1)
        self.play(Create(code1), run_time=2)
        self.wait(3)

        # 清除第一种方法
        self.play(FadeOut(method1_title), FadeOut(code1), run_time=1)

        # 方法二：预处理数组
        method2_title = Text("方法二：预处理数组", font_size=24, color=GREEN)
        method2_title.move_to(UP * 2.5)

        code_lines_2 = [
            "int trap(vector<int>& height) {",
            "    int n = height.size();",
            "    vector<int> left_max(n), right_max(n);",
            "",
            "    left_max[0] = height[0];",
            "    for (int i = 1; i < n; i++)",
            "        left_max[i] = max(left_max[i-1], height[i]);",
            "",
            "    right_max[n-1] = height[n-1];",
            "    for (int i = n-2; i >= 0; i--)",
            "        right_max[i] = max(right_max[i+1], height[i]);",
            "",
            "    int result = 0;",
            "    for (int i = 0; i < n; i++)",
            "        result += min(left_max[i], right_max[i]) - height[i];",
            "    return result;",
            "}"
        ]

        code2 = VGroup()
        for i, line in enumerate(code_lines_2):
            if line.strip():
                text_line = Text(line, font_size=16, color=WHITE, font="Courier New")
            else:
                text_line = Text(" ", font_size=16)
            text_line.move_to(UP * (1.6 - i * 0.19))
            code2.add(text_line)

        code2.scale(0.75)
        code2.move_to(DOWN * 0.3)

        self.play(Write(method2_title), run_time=1)
        self.play(Create(code2), run_time=2)
        self.wait(3)

        # 清除代码
        self.play(FadeOut(code_title), FadeOut(method2_title), FadeOut(code2), run_time=1)

    def show_final_demo(self, height):
        """最终演示"""
        # 标题
        demo_title = Text("最终演示：接雨水可视化", font_size=36, color=WHITE, weight=BOLD)
        demo_title.to_edge(UP)
        self.play(Write(demo_title), run_time=2)
        
        # 重新创建图表并显示雨水
        self.create_final_visualization(height)
        
        self.wait(4)

    def create_final_visualization(self, height):
        """创建最终可视化"""
        # 计算雨水量
        n = len(height)
        left_max = [0] * n
        right_max = [0] * n
        
        left_max[0] = height[0]
        for i in range(1, n):
            left_max[i] = max(left_max[i-1], height[i])
        
        right_max[n-1] = height[n-1]
        for i in range(n-2, -1, -1):
            right_max[i] = max(right_max[i+1], height[i])
        
        water_amounts = []
        total_water = 0
        for i in range(n):
            water = max(0, min(left_max[i], right_max[i]) - height[i])
            water_amounts.append(water)
            total_water += water
        
        # 创建坐标轴
        axes = Axes(
            x_range=[-0.5, n - 0.5, 1],
            y_range=[0, max(height) + 1, 1],
            x_length=10,
            y_length=4,
            axis_config={"color": GRAY, "stroke_width": 2}
        )
        axes.move_to(DOWN * 0.5)
        
        self.play(Create(axes), run_time=1)
        
        # 创建柱子和雨水
        bars = VGroup()
        waters = VGroup()
        
        for i in range(n):
            # 柱子
            if height[i] > 0:
                bar = Rectangle(
                    width=0.7,
                    height=height[i] * 0.8,
                    fill_color=BLUE_D,
                    fill_opacity=0.9,
                    stroke_color=WHITE,
                    stroke_width=2
                )
                bar.move_to(axes.c2p(i, height[i] * 0.8 / 2))
                bars.add(bar)
            
            # 雨水
            if water_amounts[i] > 0:
                water = Rectangle(
                    width=0.7,
                    height=water_amounts[i] * 0.8,
                    fill_color=BLUE_C,
                    fill_opacity=0.7,
                    stroke_color=BLUE_B,
                    stroke_width=1
                )
                water.move_to(axes.c2p(i, height[i] * 0.8 + water_amounts[i] * 0.8 / 2))
                waters.add(water)
        
        # 动画显示
        self.play(Create(bars), run_time=2)
        self.wait(1)
        
        # 显示雨水
        explanation = Text("蓝色区域表示接到的雨水", font_size=20, color=BLUE_C)
        explanation.move_to(DOWN * 3)
        self.play(Write(explanation), run_time=1)
        
        self.play(Create(waters), run_time=2)
        
        # 显示结果
        result = Text(f"总雨水量：{total_water} 个单位", font_size=24, color=YELLOW, weight=BOLD)
        result.move_to(DOWN * 3.5)
        self.play(Write(result), run_time=2)
        
        self.wait(2)
