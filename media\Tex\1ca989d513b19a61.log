This is pdfTeX, Version 3.141592653-2.6-1.40.27 (MiKTeX 25.4) (preloaded format=latex 2025.7.28)  30 JUL 2025 01:45
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**./media/Tex/1ca989d513b19a61.tex
(media/Tex/1ca989d513b19a61.tex
LaTeX2e <2025-06-01> patch level 1
L3 programming layer <2025-07-19>
(E:\Programs\MiKTeX\tex/latex/standalone\standalone.cls
Document Class: standalone 2025/02/22 v1.5a Class to compile TeX sub-files stan
dalone
(E:\Programs\MiKTeX\tex/latex/tools\shellesc.sty
Package: shellesc 2023/07/08 v1.0d unified shell escape interface for LaTeX
Package shellesc Info: Restricted shell escape enabled on input line 77.
) (E:\Programs\MiKTeX\tex/generic/iftex\ifluatex.sty
Package: ifluatex 2019/10/25 v1.5 ifluatex legacy package. Use iftex instead.
(E:\Programs\MiKTeX\tex/generic/iftex\iftex.sty
Package: iftex 2024/12/12 v1.0g TeX engine tests
)) (E:\Programs\MiKTeX\tex/latex/xkeyval\xkeyval.sty
Package: xkeyval 2022/06/16 v2.9 package option processing (HA)
(E:\Programs\MiKTeX\tex/generic/xkeyval\xkeyval.tex (E:\Programs\MiKTeX\tex/gen
eric/xkeyval\xkvutils.tex
\XKV@toks=\toks17
\XKV@tempa@toks=\toks18
(E:\Programs\MiKTeX\tex/generic/xkeyval\keyval.tex))
\XKV@depth=\count275
File: xkeyval.tex 2014/12/03 v2.7a key=value parser (HA)
))
\sa@internal=\count276
\c@sapage=\count277
(E:\Programs\MiKTeX\tex/latex/standalone\standalone.cfg
File: standalone.cfg 2025/02/22 v1.5a Default configuration file for 'standalon
e' class
) (E:\Programs\MiKTeX\tex/latex/base\article.cls
Document Class: article 2025/01/22 v1.4n Standard LaTeX document class
(E:\Programs\MiKTeX\tex/latex/base\size10.clo
File: size10.clo 2025/01/22 v1.4n Standard LaTeX file (size option)
)
\c@part=\count278
\c@section=\count279
\c@subsection=\count280
\c@subsubsection=\count281
\c@paragraph=\count282
\c@subparagraph=\count283
\c@figure=\count284
\c@table=\count285
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\bibindent=\dimen148
) (E:\Programs\MiKTeX\tex/latex/preview\preview.sty
Package: preview 2024/06/30 14.0.6 (AUCTeX/preview-latex)
(E:\Programs\MiKTeX\tex/latex/luatex85\luatex85.sty
Package: luatex85 2016/06/15 v1.4 pdftex aliases for luatex
) (E:\Programs\MiKTeX\tex/latex/preview\prtightpage.def
\PreviewBorder=\dimen149
)
\pr@snippet=\count286
\pr@box=\box53
\pr@output=\toks19
)) (E:\Programs\MiKTeX\tex/generic/babel\babel.sty
Package: babel 2025/07/13 v25.11 The multilingual framework for LuaLaTeX, pdfLa
TeX and XeLaTeX
\babel@savecnt=\count287
LaTeX Encoding Info:    Redeclaring text command \ij (encoding OT1) on input li
ne 2045.
LaTeX Encoding Info:    Redeclaring text command \IJ (encoding OT1) on input li
ne 2047.
LaTeX Encoding Info:    Redeclaring text command \ij (encoding T1) on input lin
e 2049.
LaTeX Encoding Info:    Redeclaring text command \IJ (encoding T1) on input lin
e 2050.
LaTeX Encoding Info:    Ignoring declaration for text command \ij (encoding ?) 
on input line 2051.
LaTeX Encoding Info:    Ignoring declaration for text command \IJ (encoding ?) 
on input line 2053.
LaTeX Encoding Info:    Ignoring declaration for text command \SS (encoding ?) 
on input line 2078.
\U@D=\dimen150
\l@unhyphenated=\language79
(E:\Programs\MiKTeX\tex/generic/babel\txtbabel.def)
\bbl@readstream=\read2
\bbl@dirlevel=\count288
*************************************
* Local config file bblopts.cfg used
*
(E:\Programs\MiKTeX\tex/latex/arabi\bblopts.cfg
File: bblopts.cfg 2005/09/08 v0.1 add Arabic and Farsi to "declared" options of
 babel
) (E:\Programs\MiKTeX\tex/latex/babel-english\english.ldf
Language: english 2017/06/06 v3.3r English support from the babel system
(E:\Programs\MiKTeX\tex/generic/babel/locale/en\babel-english.tex
Package babel Info: Importing font and identification data for english
(babel)             from babel-en.ini. Reported on input line 12.
)
Package babel Info: Hyphen rules for 'canadian' set to \l@english
(babel)             (\language0). Reported on input line 102.
Package babel Info: Hyphen rules for 'australian' set to \l@ukenglish
(babel)             (\language73). Reported on input line 105.
Package babel Info: Hyphen rules for 'newzealand' set to \l@ukenglish
(babel)             (\language73). Reported on input line 108.
)) (E:\Programs\MiKTeX\tex/latex/amsmath\amsmath.sty
Package: amsmath 2025/06/16 v2.17y AMS math features
\@mathmargin=\skip51
For additional information on amsmath, use the `?' option.
(E:\Programs\MiKTeX\tex/latex/amsmath\amstext.sty
Package: amstext 2024/11/17 v2.01 AMS text
(E:\Programs\MiKTeX\tex/latex/amsmath\amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks20
\ex@=\dimen151
)) (E:\Programs\MiKTeX\tex/latex/amsmath\amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen152
) (E:\Programs\MiKTeX\tex/latex/amsmath\amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count289
LaTeX Info: Redefining \frac on input line 233.
\uproot@=\count290
\leftroot@=\count291
LaTeX Info: Redefining \overline on input line 398.
LaTeX Info: Redefining \colon on input line 409.
\classnum@=\count292
\DOTSCASE@=\count293
LaTeX Info: Redefining \ldots on input line 495.
LaTeX Info: Redefining \dots on input line 498.
LaTeX Info: Redefining \cdots on input line 619.
\Mathstrutbox@=\box54
\strutbox@=\box55
LaTeX Info: Redefining \big on input line 721.
LaTeX Info: Redefining \Big on input line 722.
LaTeX Info: Redefining \bigg on input line 723.
LaTeX Info: Redefining \Bigg on input line 724.
\big@size=\dimen153
LaTeX Font Info:    Redeclaring font encoding OML on input line 742.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 743.
\macc@depth=\count294
LaTeX Info: Redefining \bmod on input line 904.
LaTeX Info: Redefining \pmod on input line 909.
LaTeX Info: Redefining \smash on input line 939.
LaTeX Info: Redefining \relbar on input line 969.
LaTeX Info: Redefining \Relbar on input line 970.
\c@MaxMatrixCols=\count295
\dotsspace@=\muskip17
\c@parentequation=\count296
\dspbrk@lvl=\count297
\tag@help=\toks21
\row@=\count298
\column@=\count299
\maxfields@=\count300
\andhelp@=\toks22
\eqnshift@=\dimen154
\alignsep@=\dimen155
\tagshift@=\dimen156
\tagwidth@=\dimen157
\totwidth@=\dimen158
\lineht@=\dimen159
\@envbody=\toks23
\multlinegap=\skip52
\multlinetaggap=\skip53
\mathdisplay@stack=\toks24
LaTeX Info: Redefining \[ on input line 2949.
LaTeX Info: Redefining \] on input line 2950.
) (E:\Programs\MiKTeX\tex/latex/amsfonts\amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols
(E:\Programs\MiKTeX\tex/latex/amsfonts\amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
)) (E:\Programs\MiKTeX\tex/latex/l3backend\l3backend-dvips.def
File: l3backend-dvips.def 2025-06-09 L3 backend support: dvips
\l__pdfannot_backend_content_box=\box56
\l__pdfannot_backend_model_box=\box57
\g__pdfannot_backend_int=\count301
\g__pdfannot_backend_link_int=\count302
\g__pdfannot_backend_link_sf_int=\count303
)
No file 1ca989d513b19a61.aux.
\openout1 = `1ca989d513b19a61.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 5.
LaTeX Font Info:    ... okay on input line 5.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 5.
LaTeX Font Info:    ... okay on input line 5.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 5.
LaTeX Font Info:    ... okay on input line 5.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 5.
LaTeX Font Info:    ... okay on input line 5.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 5.
LaTeX Font Info:    ... okay on input line 5.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 5.
LaTeX Font Info:    ... okay on input line 5.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 5.
LaTeX Font Info:    ... okay on input line 5.
Preview: Fontsize 10pt
LaTeX Font Info:    Trying to load font information for U+msa on input line 6.
(E:\Programs\MiKTeX\tex/latex/amsfonts\umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 6.
(E:\Programs\MiKTeX\tex/latex/amsfonts\umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)
LaTeX Font Info:    Trying to load font information for OMS+cmtt on input line 
8.
LaTeX Font Info:    No file OMScmtt.fd. on input line 8.

LaTeX Font Warning: Font shape `OMS/cmtt/m/n' undefined
(Font)              using `OMS/cmsy/m/n' instead
(Font)              for symbol `textbraceleft' on input line 8.


! LaTeX Error: Unicode character 类 (U+7C7B)
               not set up for use with LaTeX.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.8 \end{align*}
                
You may provide a definition with
\DeclareUnicodeCharacter 

 
Here is how much of TeX's memory you used:
 3542 strings out of 468150
 57246 string characters out of 5443614
 465296 words of memory out of 5000000
 32130 multiletter control sequences out of 15000+600000
 629028 words of font info for 51 fonts, out of 8000000 for 9000
 1141 hyphenation exceptions out of 8191
 70i,17n,71p,736b,329s stack positions out of 10000i,1000n,20000p,200000b,200000s
No pages of output.
