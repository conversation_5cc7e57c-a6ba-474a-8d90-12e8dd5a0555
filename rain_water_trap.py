from manim import *
import numpy as np

class RainWaterTrap(Scene):
    def construct(self):
        # 设置背景颜色
        self.camera.background_color = "#1e1e1e"

        # 示例数据
        height = [0, 1, 0, 2, 1, 0, 1, 3, 2, 1, 2, 1]

        # 创建标题并显示
        self.show_title()

        # 显示题目描述
        self.show_problem_description(height)

        # 显示解题思路
        self.show_solution_approach(height)

        # 显示代码实现
        self.show_code_implementations()

        # 最终可视化
        self.final_visualization(height)

    def show_title(self):
        """显示标题"""
        title = Text("力扣第42题：接雨水", font_size=48, color=WHITE)
        title.to_edge(UP)
        self.play(Write(title), run_time=2)
        self.wait(1)

        # 将标题移动到更合适的位置
        self.play(title.animate.scale(0.8).to_corner(UL), run_time=1)
        self.title = title

    def show_problem_description(self, height):
        """显示题目描述和示例"""
        # 题目描述
        description = Text(
            "给定 n 个非负整数表示每个宽度为 1 的柱子的高度图，\n计算按此排列的柱子，下雨之后能接多少雨水。",
            font_size=28,
            color=YELLOW,
            line_spacing=1.2
        )
        description.move_to(UP * 2)

        self.play(Write(description), run_time=3)
        self.wait(2)

        # 显示示例数据
        example_text = Text("示例：height = [0,1,0,2,1,0,1,3,2,1,2,1]", font_size=24, color=BLUE_C)
        example_text.next_to(description, DOWN, buff=0.5)

        self.play(Write(example_text), run_time=2)
        self.wait(1)

        # 创建初始柱状图
        self.create_bar_chart(height, description, example_text)

    def create_bar_chart(self, height, description=None, example_text=None):
        """创建初始柱状图"""
        bars = VGroup()
        bar_width = 0.6
        bar_spacing = 0.8
        scale_factor = 0.6

        # 创建柱子
        for i, h in enumerate(height):
            x_pos = i * bar_spacing - len(height) * bar_spacing / 2 + bar_spacing / 2

            if h > 0:
                bar = Rectangle(
                    width=bar_width,
                    height=h * scale_factor,
                    fill_color=BLUE,
                    fill_opacity=0.8,
                    stroke_color=WHITE,
                    stroke_width=2
                )
                bar.move_to([x_pos, h * scale_factor / 2 - 0.5, 0])
            else:
                # 高度为0的柱子用小矩形表示
                bar = Rectangle(
                    width=bar_width,
                    height=0.1,
                    fill_color=GRAY,
                    fill_opacity=0.5,
                    stroke_color=WHITE,
                    stroke_width=1
                )
                bar.move_to([x_pos, -0.45, 0])
            bars.add(bar)

        # 添加数值标签
        labels = VGroup()
        for i, h in enumerate(height):
            label = Text(str(h), font_size=20, color=WHITE)
            x_pos = i * bar_spacing - len(height) * bar_spacing / 2 + bar_spacing / 2
            label.move_to([x_pos, -1.2, 0])
            labels.add(label)

        # 添加索引标签
        indices = VGroup()
        for i in range(len(height)):
            index = Text(str(i), font_size=16, color=GRAY)
            x_pos = i * bar_spacing - len(height) * bar_spacing / 2 + bar_spacing / 2
            index.move_to([x_pos, -1.6, 0])
            indices.add(index)

        # 动画显示柱状图
        self.play(Create(bars), run_time=2)
        self.play(Write(labels), Write(indices), run_time=1.5)

        self.bars = bars
        self.labels = labels
        self.indices = indices

        # 移除描述文本，为解题思路让出空间
        if description and example_text:
            self.play(
                FadeOut(description),
                FadeOut(example_text),
                run_time=1
            )

        self.wait(1)



    def show_solution_approach(self, height):
        """展示解题思路"""
        # 显示思路标题
        approach_title = Text("解题思路", font_size=36, color=YELLOW)
        approach_title.move_to(UP * 2.5)
        self.play(Write(approach_title), run_time=1.5)

        # 显示核心思路
        core_idea = Text(
            "对于每个位置 i，能存的水量 = min(左边最高, 右边最高) - height[i]",
            font_size=28,
            color=WHITE
        )
        core_idea.move_to(UP * 1.8)
        self.play(Write(core_idea), run_time=2)
        self.wait(2)

        # 显示公式
        formula = MathTex(
            r"water[i] = \min(lmax[i], rmax[i]) - height[i]",
            font_size=32,
            color=GREEN
        )
        formula.move_to(UP * 1.2)
        self.play(Write(formula), run_time=2)
        self.wait(2)

        # 演示具体位置的计算
        self.demonstrate_calculation(height)

        # 计算并显示左右最大值数组
        self.show_left_right_max(height)

        # 清除思路相关文本
        self.play(
            FadeOut(approach_title),
            FadeOut(core_idea),
            FadeOut(formula),
            run_time=1
        )

    def demonstrate_calculation(self, height):
        """演示具体位置的计算过程"""
        # 选择一个有代表性的位置进行演示（位置2，height=0）
        demo_index = 2

        # 高亮选中的柱子
        highlight = Rectangle(
            width=0.8,
            height=3,
            fill_opacity=0,
            stroke_color=YELLOW,
            stroke_width=4
        )
        x_pos = demo_index * 0.8 - len(height) * 0.8 / 2 + 0.8 / 2
        highlight.move_to([x_pos, 0.5, 0])

        self.play(Create(highlight), run_time=1)

        # 显示计算过程
        calc_text = Text(f"位置 {demo_index}: height = {height[demo_index]}", font_size=24, color=YELLOW)
        calc_text.move_to(DOWN * 0.5)
        self.play(Write(calc_text), run_time=1)

        # 计算左边最大值
        left_max = max(height[:demo_index + 1])
        left_text = Text(f"左边最大值 = {left_max}", font_size=24, color=GREEN)
        left_text.move_to(DOWN * 1)
        self.play(Write(left_text), run_time=1)

        # 计算右边最大值
        right_max = max(height[demo_index:])
        right_text = Text(f"右边最大值 = {right_max}", font_size=24, color=RED)
        right_text.move_to(DOWN * 1.5)
        self.play(Write(right_text), run_time=1)

        # 计算结果
        water_amount = min(left_max, right_max) - height[demo_index]
        result_text = Text(f"可存水量 = min({left_max}, {right_max}) - {height[demo_index]} = {water_amount}",
                          font_size=24, color=BLUE_C)
        result_text.move_to(DOWN * 2)
        self.play(Write(result_text), run_time=1.5)

        self.wait(2)

        # 清除演示
        self.play(
            FadeOut(highlight),
            FadeOut(calc_text),
            FadeOut(left_text),
            FadeOut(right_text),
            FadeOut(result_text),
            run_time=1
        )

    def show_left_right_max(self, height):
        """显示左右最大值数组的计算过程"""
        # 计算左边最大值数组
        left_max = [0] * (len(height) + 1)
        for i in range(len(height)):
            left_max[i + 1] = max(left_max[i], height[i])

        # 计算右边最大值数组
        right_max = [0] * (len(height) + 1)
        for i in range(len(height) - 1, -1, -1):
            right_max[i] = max(right_max[i + 1], height[i])

        # 显示算法步骤标题
        step_title = Text("算法步骤：计算左右最大值数组", font_size=32, color=YELLOW)
        step_title.move_to(UP * 2)
        self.play(Write(step_title), run_time=1.5)

        # 显示左边最大值数组
        left_title = Text("左边最大值数组 (lmax):", font_size=24, color=GREEN)
        left_title.move_to(UP * 1.2 + LEFT * 2)

        # 格式化左边最大值数组显示
        left_values_str = "[" + ", ".join(str(x) for x in left_max[1:]) + "]"
        left_values = Text(left_values_str, font_size=20, color=GREEN)
        left_values.next_to(left_title, DOWN, buff=0.3)

        self.play(Write(left_title), run_time=1)
        self.play(Write(left_values), run_time=1.5)
        self.wait(1)

        # 显示右边最大值数组
        right_title = Text("右边最大值数组 (rmax):", font_size=24, color=RED)
        right_title.move_to(UP * 1.2 + RIGHT * 2)

        # 格式化右边最大值数组显示
        right_values_str = "[" + ", ".join(str(x) for x in right_max[1:]) + "]"
        right_values = Text(right_values_str, font_size=20, color=RED)
        right_values.next_to(right_title, DOWN, buff=0.3)

        self.play(Write(right_title), run_time=1)
        self.play(Write(right_values), run_time=1.5)
        self.wait(2)

        # 显示计算公式
        formula_text = Text("每个位置的雨水量 = min(lmax[i], rmax[i]) - height[i]",
                           font_size=24, color=WHITE)
        formula_text.move_to(DOWN * 0.5)
        self.play(Write(formula_text), run_time=2)
        self.wait(2)

        # 清除这些文本
        self.play(
            FadeOut(step_title),
            FadeOut(left_title),
            FadeOut(left_values),
            FadeOut(right_title),
            FadeOut(right_values),
            FadeOut(formula_text),
            run_time=1.5
        )

    def show_code_implementations(self):
        """显示代码实现"""
        # 清除之前的元素
        self.play(
            FadeOut(self.bars),
            FadeOut(self.labels),
            FadeOut(self.indices),
            run_time=1
        )

        # 显示代码标题
        code_section_title = Text("C++ 代码实现", font_size=40, color=YELLOW)
        code_section_title.to_edge(UP)
        self.play(Write(code_section_title), run_time=1.5)

        # 第一种实现
        self.show_first_implementation()

        # 第二种实现（使用 std::partial_sum）
        self.show_second_implementation()

        # 第三种实现（总面积思路）
        self.show_third_implementation()

        # 清除代码标题
        self.play(FadeOut(code_section_title), run_time=1)

    def show_first_implementation(self):
        """显示第一种实现"""
        subtitle = Text("方法一：直接计算", font_size=28, color=GREEN)
        subtitle.move_to(UP * 2.5)

        code1 = Tex(
            r"\texttt{auto size = height.size();} \\",
            r"\texttt{std::vector<int> left(size + 1), right(size + 1);} \\",
            r"\\",
            r"\texttt{for (std::size\_t i = 0; i != size; ++i) \{} \\",
            r"\texttt{~~~~left[i + 1] = std::max(left[i], height[i]);} \\",
            r"\texttt{\}} \\",
            r"\texttt{for (std::size\_t i = size - 1; i + 1 != 0; --i) \{} \\",
            r"\texttt{~~~~right[i] = std::max(right[i + 1], height[i]);} \\",
            r"\texttt{\}} \\",
            r"\\",
            r"\texttt{int ans = 0;} \\",
            r"\texttt{for (std::size\_t i = 0; i < size; ++i) \{} \\",
            r"\texttt{~~~~ans += std::min(left[i + 1], right[i + 1]) - height[i];} \\",
            r"\texttt{\}}",
            font_size=16,
            color=WHITE
        )
        code1.scale(0.7)
        code1.move_to(DOWN * 0.3)

        self.play(Write(subtitle), run_time=1)
        self.play(Create(code1), run_time=2)
        self.wait(3)

        # 清除第一种实现
        self.play(FadeOut(subtitle), FadeOut(code1), run_time=1)

    def show_second_implementation(self):
        """显示第二种实现"""
        subtitle = Text("方法二：使用 std::partial_sum", font_size=28, color=GREEN)
        subtitle.move_to(UP * 2.5)

        code2 = Tex(
            r"\texttt{auto size = height.size();} \\",
            r"\texttt{std::vector<int> left(size), right(size);} \\",
            r"\\",
            r"\texttt{std::partial\_sum(height.begin(), height.end(),} \\",
            r"\texttt{~~~~~~~~~~~~~~~~left.begin(), std::ranges::max);} \\",
            r"\texttt{std::partial\_sum(height.rbegin(), height.rend(),} \\",
            r"\texttt{~~~~~~~~~~~~~~~~right.rbegin(), std::ranges::max);} \\",
            r"\\",
            r"\texttt{int ans = 0;} \\",
            r"\texttt{for (std::size\_t i = 0; i < size; ++i) \{} \\",
            r"\texttt{~~~~ans += std::min(left[i], right[i]) - height[i];} \\",
            r"\texttt{\}}",
            font_size=16,
            color=WHITE
        )
        code2.scale(0.7)
        code2.move_to(DOWN * 0.3)

        self.play(Write(subtitle), run_time=1)
        self.play(Create(code2), run_time=2)
        self.wait(3)

        # 清除第二种实现
        self.play(FadeOut(subtitle), FadeOut(code2), run_time=1)

    def show_third_implementation(self):
        """显示第三种实现"""
        subtitle = Text("方法三：总面积思路", font_size=28, color=GREEN)
        subtitle.move_to(UP * 2.5)

        explanation = Text(
            "思路：计算总的容器面积，然后减去所有柱子的面积",
            font_size=20,
            color=BLUE_C
        )
        explanation.move_to(UP * 2)

        code3 = Tex(
            r"\texttt{std::vector<int> left(height.size()), right(height.size());} \\",
            r"\\",
            r"\texttt{std::partial\_sum(height.begin(), height.end(),} \\",
            r"\texttt{~~~~~~~~~~~~~~~~left.begin(), std::ranges::max);} \\",
            r"\texttt{std::partial\_sum(height.rbegin(), height.rend(),} \\",
            r"\texttt{~~~~~~~~~~~~~~~~right.rbegin(), std::ranges::max);} \\",
            r"\\",
            r"\texttt{int ans = 0;} \\",
            r"\texttt{for (std::size\_t i = 0; i < height.size(); ++i) \{} \\",
            r"\texttt{~~~~ans += std::min(left[i], right[i]);} \\",
            r"\texttt{\}} \\",
            r"\texttt{ans -= std::accumulate(height.begin(), height.end(), 0);}",
            font_size=16,
            color=WHITE
        )
        code3.scale(0.7)
        code3.move_to(DOWN * 0.5)

        self.play(Write(subtitle), run_time=1)
        self.play(Write(explanation), run_time=1.5)
        self.play(Create(code3), run_time=2)
        self.wait(4)

        # 清除第三种实现
        self.play(FadeOut(subtitle), FadeOut(explanation), FadeOut(code3), run_time=1)

    def final_visualization(self, height):
        """最终可视化展示雨水"""
        # 显示最终可视化标题
        final_title = Text("最终可视化：接雨水效果", font_size=40, color=WHITE)
        final_title.to_edge(UP)
        self.play(Write(final_title), run_time=2)

        # 重新创建更精美的柱状图
        bars = VGroup()
        water_areas = VGroup()
        bar_width = 0.6
        bar_spacing = 0.8
        scale_factor = 0.6

        # 计算每个位置能接的雨水
        left_max = [0] * (len(height) + 1)
        for i in range(len(height)):
            left_max[i + 1] = max(left_max[i], height[i])

        right_max = [0] * (len(height) + 1)
        for i in range(len(height) - 1, -1, -1):
            right_max[i] = max(right_max[i + 1], height[i])

        water_amounts = []
        total_water = 0
        for i in range(len(height)):
            water = max(0, min(left_max[i + 1], right_max[i + 1]) - height[i])
            water_amounts.append(water)
            total_water += water

        # 创建柱子
        for i, h in enumerate(height):
            x_pos = i * bar_spacing - len(height) * bar_spacing / 2 + bar_spacing / 2

            if h > 0:
                bar = Rectangle(
                    width=bar_width,
                    height=h * scale_factor,
                    fill_color=BLUE,
                    fill_opacity=0.9,
                    stroke_color=WHITE,
                    stroke_width=2
                )
                bar.move_to([x_pos, h * scale_factor / 2 - 0.5, 0])
            else:
                # 高度为0的位置用小矩形表示地面
                bar = Rectangle(
                    width=bar_width,
                    height=0.1,
                    fill_color=GRAY,
                    fill_opacity=0.7,
                    stroke_color=WHITE,
                    stroke_width=1
                )
                bar.move_to([x_pos, -0.45, 0])
            bars.add(bar)

        # 创建雨水区域
        for i, water_height in enumerate(water_amounts):
            if water_height > 0:
                x_pos = i * bar_spacing - len(height) * bar_spacing / 2 + bar_spacing / 2
                base_height = height[i] * scale_factor

                water = Rectangle(
                    width=bar_width,
                    height=water_height * scale_factor,
                    fill_color=BLUE_C,
                    fill_opacity=0.7,
                    stroke_color=BLUE_D,
                    stroke_width=1
                )
                water.move_to([x_pos, base_height + water_height * scale_factor / 2 - 0.5, 0])
                water_areas.add(water)

        # 添加数值标签
        labels = VGroup()
        for i, h in enumerate(height):
            label = Text(str(h), font_size=18, color=WHITE)
            x_pos = i * bar_spacing - len(height) * bar_spacing / 2 + bar_spacing / 2
            label.move_to([x_pos, -1.2, 0])
            labels.add(label)

        # 添加雨水量标签
        water_labels = VGroup()
        for i, w in enumerate(water_amounts):
            if w > 0:
                label = Text(str(w), font_size=16, color=BLUE_C, weight=BOLD)
                x_pos = i * bar_spacing - len(height) * bar_spacing / 2 + bar_spacing / 2
                base_height = height[i] * scale_factor
                label.move_to([x_pos, base_height + w * scale_factor / 2 - 0.5, 0])
                water_labels.add(label)

        # 动画显示柱子
        self.play(Create(bars), run_time=2)
        self.play(Write(labels), run_time=1)
        self.wait(1)

        # 显示说明文字
        explanation = Text("蓝色区域表示可以接到的雨水", font_size=24, color=BLUE_C)
        explanation.move_to(DOWN * 2.5)
        self.play(Write(explanation), run_time=1.5)

        # 逐个显示雨水区域，带有动画效果
        for i, water in enumerate(water_areas):
            self.play(FadeIn(water), run_time=0.4)
            if i < len(water_labels):
                self.play(FadeIn(water_labels[i]), run_time=0.2)

        self.wait(1)

        # 显示最终结果
        result_text = Text(f"答案：总共可以接 {total_water} 个单位的雨水",
                          font_size=32, color=YELLOW, weight=BOLD)
        result_text.move_to(DOWN * 3.2)
        self.play(Write(result_text), run_time=2)

        # 添加一些装饰效果
        self.add_decorative_effects()

        self.wait(4)

    def add_decorative_effects(self):
        """添加装饰效果"""
        # 创建一些雨滴效果
        raindrops = VGroup()
        for _ in range(8):
            x = np.random.uniform(-6, 6)
            y = np.random.uniform(2, 4)
            raindrop = Dot(point=[x, y, 0], radius=0.05, color=BLUE_B)
            raindrops.add(raindrop)

        # 雨滴下落动画
        self.play(
            *[raindrop.animate.shift(DOWN * 6) for raindrop in raindrops],
            run_time=2,
            rate_func=linear
        )

        # 清除雨滴
        self.remove(*raindrops)
