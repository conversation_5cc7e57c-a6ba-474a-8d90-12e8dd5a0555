\documentclass[preview]{standalone}
\usepackage[english]{babel}
\usepackage{amsmath}
\usepackage{amssymb}
\begin{document}
\begin{center}
\texttt{auto size = height.size();} \\\texttt{std::vector<int> left(size + 1), right(size + 1);} \\\\\texttt{for (std::size\_t i = 0; i != size; ++i) \{} \\\texttt{~~~~left[i + 1] = std::max(left[i], height[i]);} \\\texttt{\}} \\\texttt{for (std::size\_t i = size - 1; i + 1 != 0; --i) \{} \\\texttt{~~~~right[i] = std::max(right[i + 1], height[i]);} \\\texttt{\}} \\\\\texttt{int ans = 0;} \\\texttt{for (std::size\_t i = 0; i < size; ++i) \{} \\\texttt{~~~~ans += std::min(left[i + 1], right[i + 1]) - height[i];} \\\texttt{\}}
\end{center}
\end{document}